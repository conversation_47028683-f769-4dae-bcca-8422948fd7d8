part of 'resume_cubit.dart';

class ResumeState extends Equatable {
  final ResumeModel? currentResume;
  final bool isLoading;
  final bool isSaving;
  final bool isExporting;
  final bool hasUnsavedChanges;
  final String? errorMessage;

  const ResumeState({
    this.currentResume,
    this.isLoading = false,
    this.isSaving = false,
    this.isExporting = false,
    this.hasUnsavedChanges = false,
    this.errorMessage,
  });

  ResumeState copyWith({
    ResumeModel? currentResume,
    bool? isLoading,
    bool? isSaving,
    bool? isExporting,
    bool? hasUnsavedChanges,
    String? errorMessage,
  }) {
    return ResumeState(
      currentResume: currentResume ?? this.currentResume,
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      isExporting: isExporting ?? this.isExporting,
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        currentResume,
        isLoading,
        isSaving,
        isExporting,
        hasUnsavedChanges,
        errorMessage,
      ];
}
