import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';

import '../models/simple_resume_model.dart';

class FirebaseResumeDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _firebaseAuth;
  final Uuid _uuid = const Uuid();

  FirebaseResumeDataSource(this._firestore, this._firebaseAuth);

  Future<ResumeModel> getResume(String resumeId) async {
    try {
      // For now, return a dummy resume
      return ResumeModel(
        id: resumeId,
        personalInfo: const PersonalInfoModel(
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          address: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA',
        ),
        summary: 'Experienced professional with expertise in software development.',
        workExperience: [],
        education: [],
        projects: [],
        skills: [],
        languages: [],
        certifications: [],
        socialMedia: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to get resume: $e');
    }
  }

  Future<List<ResumeModel>> getUserResumes(String userId) async {
    try {
      // For now, return empty list
      return [];
    } catch (e) {
      throw Exception('Failed to get user resumes: $e');
    }
  }

  Future<void> saveResume(ResumeModel resume) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // For now, just simulate saving
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      throw Exception('Failed to save resume: $e');
    }
  }

  Future<void> deleteResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // For now, just simulate deletion
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      throw Exception('Failed to delete resume: $e');
    }
  }

  Future<ResumeModel> duplicateResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final originalResume = await getResume(resumeId);
      final newResumeId = _uuid.v4();

      final duplicatedResume = originalResume.copyWith(
        id: newResumeId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      return duplicatedResume;
    } catch (e) {
      throw Exception('Failed to duplicate resume: $e');
    }
  }

  Future<String> shareResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Create a public share link
      final shareData = {
        'resumeId': resumeId,
        'userId': user.uid,
        'createdAt': DateTime.now().toIso8601String(),
        'isPublic': true,
      };

      final shareDoc = await _firestore
          .collection('shared_resumes')
          .add(shareData);

      // Return the share URL
      return 'https://your-app-domain.com/shared/${shareDoc.id}';
    } catch (e) {
      throw Exception('Failed to share resume: $e');
    }
  }


}
