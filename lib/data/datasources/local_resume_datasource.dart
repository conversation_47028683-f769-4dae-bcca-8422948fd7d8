import 'package:hive/hive.dart';
import '../models/simple_resume_model.dart';
import '../../core/constants/app_constants.dart';

class LocalResumeDataSource {
  Future<Box> _getResumeBox() async {
    return await Hive.openBox(AppConstants.resumeBox);
  }

  Future<void> saveResume(ResumeModel resume) async {
    try {
      // For now, just simulate saving locally
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      throw Exception('Failed to save resume locally: $e');
    }
  }

  Future<ResumeModel?> getResume() async {
    try {
      // For now, return null
      return null;
    } catch (e) {
      throw Exception('Failed to get local resume: $e');
    }
  }

  Future<void> deleteResume() async {
    try {
      final box = await _getResumeBox();
      await box.delete(AppConstants.resumeDataKey);
    } catch (e) {
      throw Exception('Failed to delete local resume: $e');
    }
  }

  Future<void> clearAllData() async {
    try {
      final box = await _getResumeBox();
      await box.clear();
    } catch (e) {
      throw Exception('Failed to clear local data: $e');
    }
  }
}
