import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/repositories/auth_repository.dart';
import '../models/user_model.dart';
import '../datasources/firebase_auth_datasource.dart';

class AuthRepositoryImpl implements AuthRepository {
  final FirebaseAuthDataSource _authDataSource;

  AuthRepositoryImpl(this._authDataSource);

  @override
  Future<UserModel?> getCurrentUser() async {
    return await _authDataSource.getCurrentUser();
  }

  @override
  Future<UserModel> signInWithEmail(String email, String password) async {
    return await _authDataSource.signInWithEmail(email, password);
  }

  @override
  Future<UserModel> signUpWithEmail(String email, String password, String displayName) async {
    return await _authDataSource.signUpWithEmail(email, password, displayName);
  }

  @override
  Future<UserModel> signInWithGoogle() async {
    return await _authDataSource.signInWithGoogle();
  }

  @override
  Future<UserModel> signInWithApple() async {
    return await _authDataSource.signInWithApple();
  }

  @override
  Future<void> signOut() async {
    return await _authDataSource.signOut();
  }

  @override
  Future<void> resetPassword(String email) async {
    return await _authDataSource.resetPassword(email);
  }

  @override
  Future<void> updateProfile(String displayName, String? photoUrl) async {
    return await _authDataSource.updateProfile(displayName, photoUrl);
  }

  @override
  Future<void> deleteAccount() async {
    return await _authDataSource.deleteAccount();
  }
}
