import 'package:equatable/equatable.dart';

class ResumeModel extends Equatable {
  final String id;
  final PersonalInfoModel personalInfo;
  final String summary;
  final List<WorkExperienceModel> workExperience;
  final List<EducationModel> education;
  final List<ProjectModel> projects;
  final List<SkillCategoryModel> skills;
  final List<LanguageModel> languages;
  final List<CertificationModel> certifications;
  final List<SocialMediaModel> socialMedia;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ResumeModel({
    required this.id,
    required this.personalInfo,
    required this.summary,
    required this.workExperience,
    required this.education,
    required this.projects,
    required this.skills,
    required this.languages,
    required this.certifications,
    required this.socialMedia,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ResumeModel.fromJson(Map<String, dynamic> json) {
    return ResumeModel(
      id: json['id'] as String,
      personalInfo: PersonalInfoModel.fromJson(json['personalInfo'] as Map<String, dynamic>),
      summary: json['summary'] as String,
      workExperience: (json['workExperience'] as List? ?? [])
          .map((e) => WorkExperienceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      education: (json['education'] as List? ?? [])
          .map((e) => EducationModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      projects: (json['projects'] as List? ?? [])
          .map((e) => ProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      skills: (json['skills'] as List? ?? [])
          .map((e) => SkillCategoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      languages: (json['languages'] as List? ?? [])
          .map((e) => LanguageModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      certifications: (json['certifications'] as List? ?? [])
          .map((e) => CertificationModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      socialMedia: (json['socialMedia'] as List? ?? [])
          .map((e) => SocialMediaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'personalInfo': personalInfo.toJson(),
      'summary': summary,
      'workExperience': workExperience.map((e) => e.toJson()).toList(),
      'education': education.map((e) => e.toJson()).toList(),
      'projects': projects.map((e) => e.toJson()).toList(),
      'skills': skills.map((e) => e.toJson()).toList(),
      'languages': languages.map((e) => e.toJson()).toList(),
      'certifications': certifications.map((e) => e.toJson()).toList(),
      'socialMedia': socialMedia.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  ResumeModel copyWith({
    String? id,
    PersonalInfoModel? personalInfo,
    String? summary,
    List<WorkExperienceModel>? workExperience,
    List<EducationModel>? education,
    List<ProjectModel>? projects,
    List<SkillCategoryModel>? skills,
    List<LanguageModel>? languages,
    List<CertificationModel>? certifications,
    List<SocialMediaModel>? socialMedia,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ResumeModel(
      id: id ?? this.id,
      personalInfo: personalInfo ?? this.personalInfo,
      summary: summary ?? this.summary,
      workExperience: workExperience ?? this.workExperience,
      education: education ?? this.education,
      projects: projects ?? this.projects,
      skills: skills ?? this.skills,
      languages: languages ?? this.languages,
      certifications: certifications ?? this.certifications,
      socialMedia: socialMedia ?? this.socialMedia,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        personalInfo,
        summary,
        workExperience,
        education,
        projects,
        skills,
        languages,
        certifications,
        socialMedia,
        createdAt,
        updatedAt,
      ];
}

class PersonalInfoModel extends Equatable {
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String country;
  final String? profileImageUrl;

  const PersonalInfoModel({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
    this.profileImageUrl,
  });

  String get fullName => '$firstName $lastName';

  PersonalInfoModel copyWith({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    String? profileImageUrl,
  }) {
    return PersonalInfoModel(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        email,
        phone,
        address,
        city,
        state,
        zipCode,
        country,
        profileImageUrl,
      ];
}

class WorkExperienceModel extends Equatable {
  final String id;
  final String jobTitle;
  final String company;
  final String location;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isCurrentJob;
  final String description;
  final List<String> achievements;

  const WorkExperienceModel({
    required this.id,
    required this.jobTitle,
    required this.company,
    required this.location,
    required this.startDate,
    this.endDate,
    required this.isCurrentJob,
    required this.description,
    required this.achievements,
  });

  WorkExperienceModel copyWith({
    String? id,
    String? jobTitle,
    String? company,
    String? location,
    DateTime? startDate,
    DateTime? endDate,
    bool? isCurrentJob,
    String? description,
    List<String>? achievements,
  }) {
    return WorkExperienceModel(
      id: id ?? this.id,
      jobTitle: jobTitle ?? this.jobTitle,
      company: company ?? this.company,
      location: location ?? this.location,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isCurrentJob: isCurrentJob ?? this.isCurrentJob,
      description: description ?? this.description,
      achievements: achievements ?? this.achievements,
    );
  }

  @override
  List<Object?> get props => [
        id,
        jobTitle,
        company,
        location,
        startDate,
        endDate,
        isCurrentJob,
        description,
        achievements,
      ];
}

class EducationModel extends Equatable {
  final String id;
  final String degree;
  final String institution;
  final String location;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isCurrentlyStudying;
  final String? gpa;
  final String? description;

  const EducationModel({
    required this.id,
    required this.degree,
    required this.institution,
    required this.location,
    required this.startDate,
    this.endDate,
    required this.isCurrentlyStudying,
    this.gpa,
    this.description,
  });

  EducationModel copyWith({
    String? id,
    String? degree,
    String? institution,
    String? location,
    DateTime? startDate,
    DateTime? endDate,
    bool? isCurrentlyStudying,
    String? gpa,
    String? description,
  }) {
    return EducationModel(
      id: id ?? this.id,
      degree: degree ?? this.degree,
      institution: institution ?? this.institution,
      location: location ?? this.location,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isCurrentlyStudying: isCurrentlyStudying ?? this.isCurrentlyStudying,
      gpa: gpa ?? this.gpa,
      description: description ?? this.description,
    );
  }

  @override
  List<Object?> get props => [
        id,
        degree,
        institution,
        location,
        startDate,
        endDate,
        isCurrentlyStudying,
        gpa,
        description,
      ];
}

class ProjectModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final List<String> technologies;
  final String? projectUrl;
  final String? githubUrl;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String> achievements;

  const ProjectModel({
    required this.id,
    required this.name,
    required this.description,
    required this.technologies,
    this.projectUrl,
    this.githubUrl,
    this.startDate,
    this.endDate,
    required this.achievements,
  });

  ProjectModel copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? technologies,
    String? projectUrl,
    String? githubUrl,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? achievements,
  }) {
    return ProjectModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      technologies: technologies ?? this.technologies,
      projectUrl: projectUrl ?? this.projectUrl,
      githubUrl: githubUrl ?? this.githubUrl,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      achievements: achievements ?? this.achievements,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        technologies,
        projectUrl,
        githubUrl,
        startDate,
        endDate,
        achievements,
      ];
}

class SkillCategoryModel extends Equatable {
  final String id;
  final String category;
  final List<SkillModel> skills;

  const SkillCategoryModel({
    required this.id,
    required this.category,
    required this.skills,
  });

  SkillCategoryModel copyWith({
    String? id,
    String? category,
    List<SkillModel>? skills,
  }) {
    return SkillCategoryModel(
      id: id ?? this.id,
      category: category ?? this.category,
      skills: skills ?? this.skills,
    );
  }

  @override
  List<Object?> get props => [id, category, skills];
}

class SkillModel extends Equatable {
  final String id;
  final String name;
  final int proficiencyLevel; // 1-5 scale

  const SkillModel({
    required this.id,
    required this.name,
    required this.proficiencyLevel,
  });

  SkillModel copyWith({
    String? id,
    String? name,
    int? proficiencyLevel,
  }) {
    return SkillModel(
      id: id ?? this.id,
      name: name ?? this.name,
      proficiencyLevel: proficiencyLevel ?? this.proficiencyLevel,
    );
  }

  @override
  List<Object?> get props => [id, name, proficiencyLevel];
}

class LanguageModel extends Equatable {
  final String id;
  final String language;
  final String proficiency; // Beginner, Intermediate, Advanced, Native

  const LanguageModel({
    required this.id,
    required this.language,
    required this.proficiency,
  });

  LanguageModel copyWith({
    String? id,
    String? language,
    String? proficiency,
  }) {
    return LanguageModel(
      id: id ?? this.id,
      language: language ?? this.language,
      proficiency: proficiency ?? this.proficiency,
    );
  }

  @override
  List<Object?> get props => [id, language, proficiency];
}

class CertificationModel extends Equatable {
  final String id;
  final String name;
  final String issuer;
  final DateTime issueDate;
  final DateTime? expiryDate;
  final String? credentialId;
  final String? credentialUrl;

  const CertificationModel({
    required this.id,
    required this.name,
    required this.issuer,
    required this.issueDate,
    this.expiryDate,
    this.credentialId,
    this.credentialUrl,
  });

  CertificationModel copyWith({
    String? id,
    String? name,
    String? issuer,
    DateTime? issueDate,
    DateTime? expiryDate,
    String? credentialId,
    String? credentialUrl,
  }) {
    return CertificationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      issuer: issuer ?? this.issuer,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      credentialId: credentialId ?? this.credentialId,
      credentialUrl: credentialUrl ?? this.credentialUrl,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        issuer,
        issueDate,
        expiryDate,
        credentialId,
        credentialUrl,
      ];
}

class SocialMediaModel extends Equatable {
  final String id;
  final String platform;
  final String url;
  final String username;

  const SocialMediaModel({
    required this.id,
    required this.platform,
    required this.url,
    required this.username,
  });

  SocialMediaModel copyWith({
    String? id,
    String? platform,
    String? url,
    String? username,
  }) {
    return SocialMediaModel(
      id: id ?? this.id,
      platform: platform ?? this.platform,
      url: url ?? this.url,
      username: username ?? this.username,
    );
  }

  @override
  List<Object?> get props => [id, platform, url, username];
}
